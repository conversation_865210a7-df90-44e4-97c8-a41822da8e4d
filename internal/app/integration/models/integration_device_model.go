package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type IntegrationDevice struct {
	commonmodel.ModelV2
	IntegrationTypeCode string
	ReferenceCode       string
	IntegrationID       *string
	AttachedAt          null.Time
	AttachedByUserID    *string

	Integration Integration `gorm:"foreignKey:IntegrationID;references:ID"`
}

func (IntegrationDevice) TableName() string {
	return "ins_integration_devices"
}

func (ia *IntegrationDevice) BeforeCreate(db *gorm.DB) error {
	ia.SetUUID("ide")
	ia.ModelV2.BeforeCreate(db)
	return nil
}

func (ia *IntegrationDevice) BeforeUpdate(db *gorm.DB) error {
	ia.ModelV2.BeforeUpdate(db)
	return nil
}

type IntegrationDeviceWhere struct {
	ID                      string
	IntegrationID           string
	IntegrationIDs          []string
	IntegrationTypeCode     string
	IntegrationTypeCodes    []string
	ReferenceCode           string
	ReferenceCodes          []string
	AttachedByUserID        string
	AttachedByUserIDs       []string
	AttachedAtStart         time.Time
	AttachedAtEnd           time.Time
	ClientID                string
	SerialOrReferenceNumber string
	AssetID                 string
	AssetIDs                []string
}

type IntegrationDevicePreload struct {
	Integration                          bool
	IntegrationAsset                     bool
	IntegrationAssetTyreWithLinkedParent bool
}

type IntegrationDeviceCondition struct {
	Where       IntegrationDeviceWhere
	Preload     IntegrationDevicePreload
	Columns     []string
	IsForUpdate bool
}

type GetIntegrationDeviceListParam struct {
	commonmodel.ListRequest
	Cond IntegrationDeviceCondition
}

type IntegrationDeviceHistory struct {
	commonmodel.ModelV2
	IntegrationID       string
	IntegrationDeviceID string
	AttachedAt          time.Time
	AttachedByUserID    string
	DetachedAt          null.Time
	DetachedByUserID    string

	IntegrationDevice IntegrationDevice `gorm:"foreignKey:IntegrationDeviceID;references:ID"`
	Integration       Integration       `gorm:"foreignKey:IntegrationID;references:ID"`
}

func (IntegrationDeviceHistory) TableName() string {
	return "ins_integration_device_histories"
}

func (ia *IntegrationDeviceHistory) BeforeCreate(db *gorm.DB) error {
	ia.SetUUID("idh")
	ia.ModelV2.BeforeCreate(db)
	return nil
}

func (ia *IntegrationDeviceHistory) BeforeUpdate(db *gorm.DB) error {
	ia.ModelV2.BeforeUpdate(db)
	return nil
}

type IntegrationDeviceHistoryWhere struct {
	ID                   string
	IntegrationID        string
	IntegrationIDs       []string
	IntegrationDeviceID  string
	IntegrationDeviceIDs []string
	AttachedByUserID     string
	AttachedByUserIDs    []string
	DetachedByUserID     string
	DetachedByUserIDs    []string
	AttachedAtStart      time.Time
	AttachedAtEnd        time.Time
	DetachedAtStart      time.Time
	DetachedAtEnd        time.Time
	ClientID             string
}

type IntegrationDeviceHistoryPreload struct {
	IntegrationDevice                                     bool
	IntegrationDeviceIntegration                          bool
	IntegrationDeviceIntegrationAsset                     bool
	IntegrationDeviceIntegrationAssetTyreWithLinkedParent bool
	Integration                                           bool
	IntegrationAsset                                      bool
	IntegrationAssetTyreWithLinkedParent                  bool
}

type IntegrationDeviceHistoryCondition struct {
	Where       IntegrationDeviceHistoryWhere
	Preload     IntegrationDeviceHistoryPreload
	Columns     []string
	IsForUpdate bool
}

type GetIntegrationDeviceHistoryListParam struct {
	commonmodel.ListRequest
	Cond IntegrationDeviceHistoryCondition
}
