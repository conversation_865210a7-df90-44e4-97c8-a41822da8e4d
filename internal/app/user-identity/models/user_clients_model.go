package models

import (
	"assetfindr/pkg/common/commonmodel"

	"gopkg.in/guregu/null.v4"
)

type UserClient struct {
	UserID                string      `gorm:"type:varchar(40);primary_key" json:"user_id"`
	ClientID              string      `gorm:"type:varchar(40);primary_key" json:"client_id"`
	PermissionGroupID     string      `json:"permission_group_id"`
	WialonAccountUsername string      `json:"wialon_account_username"`
	WialonAccountID       string      `json:"wialon_account_id"`
	IsBlockWialonAccount  *bool       `json:"is_block_wialon_account"`
	DepartmentID          null.String `json:"department_id" gorm:"type:varchar(40);default:null"`
	commonmodel.BasicModel

	Group      PermissionGroup `gorm:"foreignkey:PermissionGroupID;association_foreignkey:ID" json:"group"`
	User       *User           `gorm:"foreignkey:UserID;association_foreignkey:ID" json:"user"`
	Client     Client          `gorm:"foreignkey:ClientID;association_foreignkey:ID" json:"client"`
	Department Department      `json:"department"`
}

func (uc UserClient) TableName() string {
	return "uis_user_clients"
}

type UserClientCondition struct {
	Where   UserClientWhere
	Preload UserClientPreload
	Columns []string
}

type UserClientWhere struct {
	UserID            string
	UserIDs           []string
	ClientID          string
	PermissionGroupID string
}

type UserClientPreload struct {
	Client     bool
	User       bool
	Department bool
}
