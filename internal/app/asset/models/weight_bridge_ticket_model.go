package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type WeightBridgeTicket struct {
	commonmodel.ModelV2
	ReferenceID                    string       `json:"reference_id"`
	VehicleAssetID                 string       `json:"vehicle_asset_id"`
	AssetVehicle                   AssetVehicle `json:"asset_vehicle" gorm:"foreignKey:VehicleAssetID;references:AssetID"`
	LoadType                       string       `json:"load_type"`
	InboundAt                      null.Time    `json:"inbound_at"`
	InboundWeightKg                int          `json:"inbound_weight_kg"`
	InboundWeightBridgeLocationID  string       `json:"inbound_weight_bridge_location_id"`
	InboundWeightBridgeLocation    Location     `json:"inbound_weight_bridge_location" gorm:"foreignKey:InboundWeightBridgeLocationID;references:ID"`
	InboundOperatorUserID          string       `json:"inbound_operator_user_id"`
	InboundOperatorUserFirstName   string       `json:"inbound_operator_user_first_name"`
	InboundOperatorUserLastName    string       `json:"inbound_operator_user_last_name"`
	InboundLocationID              string       `json:"inbound_location_id"`
	InboundLocation                Location     `json:"inbound_location" gorm:"foreignKey:InboundLocationID;references:ID"`
	OutboundAt                     null.Time    `json:"outbound_at"`
	OutboundWeightKg               int          `json:"outbound_weight_kg"`
	OutboundWeightBridgeLocationID string       `json:"outbound_weight_bridge_location_id"`
	OutboundWeightBridgeLocation   Location     `json:"outbound_weight_bridge_location" gorm:"foreignKey:OutboundWeightBridgeLocationID;references:ID"`
	OutboundOperatorUserID         string       `json:"outbound_operator_user_id"`
	OutboundOperatorUserFirstName  string       `json:"outbound_operator_user_first_name"`
	OutboundOperatorUserLastName   string       `json:"outbound_operator_user_last_name"`
	OutboundLocationID             string       `json:"outbound_location_id"`
	OutboundLocation               Location     `json:"outbound_location" gorm:"foreignKey:OutboundLocationID;references:ID"`
	NetWeight                      float64      `json:"net_weight"`
	WorkShiftID                    string       `json:"work_shift_id"`
	WorkShiftAlias                 string       `json:"work_shift_alias"`
	WorkShiftName                  string       `json:"work_shift_name"`
	Remark                         string       `json:"remark"`
	MaterialID                     string       `json:"material_id"`
	TicketDate                     null.Time    `json:"ticket_date"`
	ReconciliationDate             null.Time    `json:"reconciliation_date"`
}

type WeightBridgeTicketWithLocationRoute struct {
	WeightBridgeTicket
	LocationRouteID        string    `json:"location_route_id"`
	StartLocationID        string    `json:"start_location_id"`
	StartLocationName      string    `json:"start_location_name"`
	EndLocationID          string    `json:"end_location_id"`
	EndLocationName        string    `json:"end_location_name"`
	OperationStartDate     null.Time `json:"operation_start_date"`
	DistanceKm             float64   `json:"distance_km"`
	IsEstimationDistanceKm bool      `json:"is_estimation_distance_km"`
}

func (wbt WeightBridgeTicket) TableName() string {
	return "ams_weight_bridge_tickets"
}

func (wbt *WeightBridgeTicket) BeforeCreate(db *gorm.DB) error {
	wbt.SetUUID("wbt")
	wbt.ModelV2.BeforeCreate(db)
	return nil
}

func (wbt *WeightBridgeTicket) BeforeUpdate(db *gorm.DB) error {
	wbt.ModelV2.BeforeUpdate(db)
	return nil
}

type WeightBridgeTicketWhere struct {
	ID                      string
	ClientID                string
	ReconciliationDateStart time.Time
	ReconciliationDateEnd   time.Time
	TicketDateStart         time.Time
	TicketDateEnd           time.Time
	LocationID              string
	VehicleAssetID          string
	WorkShiftID             string
	VehicleAssetIDs         []string
	WorkShiftIDs            []string
	MaterialIDs             []string
	RouteStartLocationID    string
	RouteEndLocationID      string
}

type WeightBridgeTicketPreload struct {
	AssetVehicle                 bool
	InboundWeightBridgeLocation  bool
	InboundLocation              bool
	OutboundWeightBridgeLocation bool
	OutboundLocation             bool
}

type WeightBridgeTicketCondition struct {
	Where   WeightBridgeTicketWhere
	Preload WeightBridgeTicketPreload
	Columns []string
}

type GetWeightBridgeTicketListParam struct {
	commonmodel.ListRequest
	Cond WeightBridgeTicketCondition
}

var WeightBridgeTicketSorts = map[string]string{
	"inbound_weight_kg":  "inbound_weight_kg",
	"outbound_weight_kg": "outbound_weight_kg",
	"net_weight":         "net_weight",
	"outbound_at":        "outbound_at",
}

type WeightBridgeTicketSummary struct {
	TotalWeight  float64 `json:"total_weight"`
	TotalTrip    int64   `json:"total_trip"`
	AvgPayload   float64 `json:"avg_payload"`
	MaxNetWeight float64 `json:"max_net_weight"`
	MinNetWeight float64 `json:"min_net_weight"`
	TotalGross   float64 `json:"total_gross"`
	AvgGross     float64 `json:"avg_gross"`
	MaxGross     float64 `json:"max_gross"`
	MinGross     float64 `json:"min_gross"`
	TotalTare    float64 `json:"total_tare"`
	AvgTare      float64 `json:"avg_tare"`
	MaxTare      float64 `json:"max_tare"`
	MinTare      float64 `json:"min_tare"`
}

type WeightBridgeTicketOutboundSummary struct {
	TotalWeight          float64 `json:"total_weight"`
	TotalTrip            int64   `json:"total_trip"`
	AvgPayload           float64 `json:"avg_payload"`
	OutboundLocationID   string  `json:"outbound_location_id"`
	OutboundLocationName string  `json:"outbound_location_name"`
}

type WeightBridgeTicketInboundSummary struct {
	TotalWeight         float64 `json:"total_weight"`
	TotalTrip           int64   `json:"total_trip"`
	AvgPayload          float64 `json:"avg_payload"`
	InboundLocationID   string  `json:"inbound_location_id"`
	InboundLocationName string  `json:"inbound_location_name"`
}
