package database

import (
	"context"
	"database/sql"
	"fmt"

	"gorm.io/gorm"
)

type dbImpl struct {
	orm *gorm.DB
	tx  *gorm.DB
}

func (db *dbImpl) GetOrm() *gorm.DB {
	return db.orm
}

func (db *dbImpl) GetTx() *gorm.DB {
	if db.tx != nil {
		return db.tx
	}

	return db.orm
}

func (db *dbImpl) BeginTx(opts ...*sql.TxOptions) (DBUsecase, error) {
	if db.tx != nil {
		return db, fmt.Errorf("transaction already created")
	}

	tx := db.orm.Begin(opts...)
	if tx.Error != nil {
		return nil, tx.Error
	}

	return &dbImpl{
		orm: db.orm,
		tx:  tx,
	}, nil
}

func (db *dbImpl) SetTxIsoRepeatableRead() error {
	return db.tx.Exec("SET TRANSACTION ISOLATION LEVEL REPEATABLE READ").Error
}

func (db *dbImpl) Commit() error {
	if db.tx == nil {
		return fmt.Errorf("got nil transaction")
	}

	return db.tx.Commit().Error
}

func (db *dbImpl) Rollback() error {
	if db.tx == nil {
		return fmt.Errorf("got nil transaction")
	}

	return db.tx.Rollback().Error
}

func (db *dbImpl) DB() DBI {
	return db
}

func (db *dbImpl) WithCtx(ctx context.Context) DBUsecase {
	return &dbImpl{
		orm: db.orm.WithContext(ctx),
	}
}

type DBI interface {
	GetOrm() *gorm.DB
	GetTx() *gorm.DB
}

type DBUsecase interface {
	DB() DBI
	WithCtx(ctx context.Context) DBUsecase
	BeginTx(opts ...*sql.TxOptions) (DBUsecase, error)
	Commit() error
	Rollback() error
}

func NewGormDBUsecase(db *gorm.DB) DBUsecase {
	return &dbImpl{
		orm: db,
		tx:  nil,
	}
}

func NewGormDBTimeSeriesUsecase(db *gorm.DB) DBUsecase {
	return &dbImpl{
		orm: db,
		tx:  nil,
	}
}
